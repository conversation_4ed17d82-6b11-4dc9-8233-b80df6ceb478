import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../widgets/custom_card.dart';
import '../../models/transaction.dart' as model;
import '../../models/account.dart';
import '../../data/database_helper.dart';
import '../../helpers/app_theme.dart';
import 'add_transaction_screen.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<model.Transaction> _transactions = [];
  List<Account> _accounts = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // 'all', 'income', 'expense'

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final transactions = await _databaseHelper.getTransactions();
      final accounts = await _databaseHelper.getAccounts();

      setState(() {
        _transactions = transactions;
        _accounts = accounts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Veri yüklenirken hata oluştu: $e')),
        );
      }
    }
  }

  Future<void> _navigateToAddTransaction([String? type]) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => AddTransactionScreen(initialType: type),
      ),
    );

    if (result == true) {
      _loadData(); // Refresh the list
    }
  }

  Future<void> _deleteTransaction(model.Transaction transaction) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('İşlemi Sil'),
        content: const Text('Bu işlemi silmek istediğinizden emin misiniz?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('İptal'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
            child: const Text('Sil'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _databaseHelper.deleteTransaction(transaction.id!);

        // Update account balance
        final account = await _databaseHelper.getAccount(transaction.accountId);
        if (account != null) {
          double newBalance = account.balance;
          if (transaction.type == 'income') {
            newBalance -= transaction.amount; // Reverse the income
          } else if (transaction.type == 'expense') {
            newBalance += transaction.amount; // Reverse the expense
          }

          await _databaseHelper.updateAccount(
            account.copyWith(balance: newBalance),
          );
        }

        _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('İşlem başarıyla silindi')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('İşlem silinirken hata oluştu: $e')),
          );
        }
      }
    }
  }

  List<model.Transaction> get _filteredTransactions {
    if (_selectedFilter == 'all') return _transactions;
    return _transactions.where((t) => t.type == _selectedFilter).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('İşlemler'),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.add),
            onSelected: (value) {
              if (value == 'income') {
                _navigateToAddTransaction('income');
              } else if (value == 'expense') {
                _navigateToAddTransaction('expense');
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'income',
                child: Row(
                  children: [
                    Icon(Icons.add_circle_outline, color: AppTheme.successColor),
                    SizedBox(width: 8),
                    Text('Gelir Ekle'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'expense',
                child: Row(
                  children: [
                    Icon(Icons.remove_circle_outline, color: AppTheme.errorColor),
                    SizedBox(width: 8),
                    Text('Gider Ekle'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: Column(
                children: [
                  // Filter Tabs
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildFilterChip('Tümü', 'all', theme),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildFilterChip('Gelir', 'income', theme),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildFilterChip('Gider', 'expense', theme),
                        ),
                      ],
                    ),
                  ),

                  // Transactions List
                  Expanded(
                    child: _filteredTransactions.isEmpty
                        ? _buildEmptyState(theme)
                        : _buildTransactionsList(theme),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildFilterChip(String label, String value, ThemeData theme) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedFilter = value;
        });
      },
      selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2),
      checkmarkColor: AppTheme.primaryColor,
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: CustomCard(
          margin: const EdgeInsets.all(32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.receipt_long_outlined,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'Henüz işlem yok',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'İlk işleminizi ekleyerek başlayın',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ElevatedButton.icon(
                    onPressed: () => _navigateToAddTransaction('income'),
                    icon: const Icon(Icons.add_circle_outline),
                    label: const Text('Gelir'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.successColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () => _navigateToAddTransaction('expense'),
                    icon: const Icon(Icons.remove_circle_outline),
                    label: const Text('Gider'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.errorColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionsList(ThemeData theme) {
    return ListView.builder(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredTransactions.length,
      itemBuilder: (context, index) {
        final transaction = _filteredTransactions[index];
        final account = _accounts.firstWhere(
          (a) => a.id == transaction.accountId,
          orElse: () => Account(name: 'Bilinmeyen Hesap', type: 'checking', balance: 0),
        );

        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildTransactionCard(transaction, account, theme),
        );
      },
    );
  }

  Widget _buildTransactionCard(model.Transaction transaction, Account account, ThemeData theme) {
    final isIncome = transaction.type == 'income';
    final color = isIncome ? AppTheme.successColor : AppTheme.errorColor;
    final icon = isIncome ? Icons.add_circle : Icons.remove_circle;

    return CustomCard(
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(
          transaction.category,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(account.name),
            if (transaction.description != null && transaction.description!.isNotEmpty)
              Text(
                transaction.description!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            Text(
              DateFormat('dd/MM/yyyy').format(transaction.date),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              '${isIncome ? '+' : '-'}${transaction.amount.toStringAsFixed(2)}',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              account.currency,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
        onTap: () => _showTransactionOptions(transaction),
      ),
    );
  }

  void _showTransactionOptions(model.Transaction transaction) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Düzenle'),
              onTap: () {
                Navigator.of(context).pop();
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => AddTransactionScreen(transaction: transaction),
                  ),
                ).then((result) {
                  if (result == true) _loadData();
                });
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: AppTheme.errorColor),
              title: const Text('Sil', style: TextStyle(color: AppTheme.errorColor)),
              onTap: () {
                Navigator.of(context).pop();
                _deleteTransaction(transaction);
              },
            ),
            ListTile(
              leading: const Icon(Icons.cancel),
              title: const Text('İptal'),
              onTap: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }
}
