import 'package:flutter/material.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../services/security_service.dart';
import '../../helpers/app_theme.dart';

class PinScreen extends StatefulWidget {
  final bool isSetup;
  final String? title;
  final String? subtitle;

  const PinScreen({
    Key? key,
    this.isSetup = false,
    this.title,
    this.subtitle,
  }) : super(key: key);

  @override
  State<PinScreen> createState() => _PinScreenState();
}

class _PinScreenState extends State<PinScreen> {
  final TextEditingController _pinController = TextEditingController();
  final SecurityService _securityService = SecurityService();
  
  String _currentPin = '';
  String _confirmPin = '';
  bool _isConfirmStep = false;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void dispose() {
    _pinController.dispose();
    super.dispose();
  }

  void _onPinChanged(String pin) {
    setState(() {
      _errorMessage = null;
    });

    if (pin.length == 4) {
      if (widget.isSetup) {
        _handleSetupPin(pin);
      } else {
        _handleVerifyPin(pin);
      }
    }
  }

  void _handleSetupPin(String pin) {
    if (!_isConfirmStep) {
      // First step: Enter new PIN
      setState(() {
        _currentPin = pin;
        _isConfirmStep = true;
        _pinController.clear();
      });
    } else {
      // Second step: Confirm PIN
      if (pin == _currentPin) {
        _setupPin(pin);
      } else {
        setState(() {
          _errorMessage = 'PIN kodları eşleşmiyor';
          _isConfirmStep = false;
          _currentPin = '';
          _pinController.clear();
        });
      }
    }
  }

  void _handleVerifyPin(String pin) async {
    setState(() {
      _isLoading = true;
    });

    final isValid = await _securityService.verifyPin(pin);
    
    if (isValid) {
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/dashboard');
      }
    } else {
      setState(() {
        _errorMessage = 'Hatalı PIN kodu';
        _pinController.clear();
        _isLoading = false;
      });
    }
  }

  void _setupPin(String pin) async {
    setState(() {
      _isLoading = true;
    });

    final success = await _securityService.setupPin(pin);
    
    if (success) {
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/dashboard');
      }
    } else {
      setState(() {
        _errorMessage = 'PIN kurulumu başarısız';
        _isConfirmStep = false;
        _currentPin = '';
        _pinController.clear();
        _isLoading = false;
      });
    }
  }

  String _getTitle() {
    if (widget.title != null) return widget.title!;
    
    if (widget.isSetup) {
      return _isConfirmStep ? 'PIN Kodunu Onayla' : 'PIN Kodu Belirle';
    }
    return 'PIN Kodunu Gir';
  }

  String _getSubtitle() {
    if (widget.subtitle != null) return widget.subtitle!;
    
    if (widget.isSetup) {
      return _isConfirmStep 
          ? 'PIN kodunu tekrar girin'
          : '4 haneli güvenlik kodunuzu belirleyin';
    }
    return 'Uygulamaya erişmek için PIN kodunuzu girin';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo or Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.security,
                  size: 40,
                  color: AppTheme.primaryColor,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Title
              Text(
                _getTitle(),
                style: theme.textTheme.headlineMedium,
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              // Subtitle
              Text(
                _getSubtitle(),
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onBackground.withOpacity(0.7),
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 48),
              
              // PIN Input
              PinCodeTextField(
                appContext: context,
                length: 4,
                controller: _pinController,
                onChanged: _onPinChanged,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(12),
                  fieldHeight: 60,
                  fieldWidth: 60,
                  activeFillColor: theme.colorScheme.surface,
                  inactiveFillColor: theme.colorScheme.surface,
                  selectedFillColor: theme.colorScheme.surface,
                  activeColor: AppTheme.primaryColor,
                  inactiveColor: theme.colorScheme.outline,
                  selectedColor: AppTheme.primaryColor,
                ),
                enableActiveFill: true,
                keyboardType: TextInputType.number,
                obscureText: true,
                obscuringCharacter: '●',
                animationType: AnimationType.fade,
                animationDuration: const Duration(milliseconds: 300),
              ),
              
              const SizedBox(height: 24),
              
              // Error Message
              if (_errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppTheme.errorColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: AppTheme.errorColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppTheme.errorColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              
              const SizedBox(height: 32),
              
              // Loading Indicator
              if (_isLoading)
                const CircularProgressIndicator(
                  color: AppTheme.primaryColor,
                ),
              
              const Spacer(),
              
              // Back Button (only for confirm step)
              if (widget.isSetup && _isConfirmStep)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _isConfirmStep = false;
                      _currentPin = '';
                      _pinController.clear();
                      _errorMessage = null;
                    });
                  },
                  child: const Text('Geri'),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
