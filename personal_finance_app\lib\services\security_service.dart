import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SecurityService {
  static const String _pinKey = 'user_pin';
  static const String _isSetupKey = 'is_setup_complete';
  static const String _biometricEnabledKey = 'biometric_enabled';

  // Hash PIN using SHA-256
  String _hashPin(String pin) {
    final bytes = utf8.encode(pin);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // Set up PIN for the first time
  Future<bool> setupPin(String pin) async {
    try {
      if (pin.length != 4) return false;
      
      final prefs = await SharedPreferences.getInstance();
      final hashedPin = _hashPin(pin);
      
      await prefs.setString(_pinKey, hashedPin);
      await prefs.setBool(_isSetupKey, true);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  // Verify PIN
  Future<bool> verifyPin(String pin) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedHashedPin = prefs.getString(_pinKey);
      
      if (storedHashedPin == null) return false;
      
      final hashedPin = _hashPin(pin);
      return hashedPin == storedHashedPin;
    } catch (e) {
      return false;
    }
  }

  // Check if PIN is set up
  Future<bool> isPinSetup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isSetupKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  // Change PIN
  Future<bool> changePin(String oldPin, String newPin) async {
    try {
      if (newPin.length != 4) return false;
      
      final isOldPinValid = await verifyPin(oldPin);
      if (!isOldPinValid) return false;
      
      final prefs = await SharedPreferences.getInstance();
      final hashedNewPin = _hashPin(newPin);
      
      await prefs.setString(_pinKey, hashedNewPin);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Reset PIN (for development/testing purposes)
  Future<bool> resetPin() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_pinKey);
      await prefs.setBool(_isSetupKey, false);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Enable/Disable biometric authentication
  Future<bool> setBiometricEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_biometricEnabledKey, enabled);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Check if biometric is enabled
  Future<bool> isBiometricEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_biometricEnabledKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  // Validate PIN format
  bool isValidPin(String pin) {
    if (pin.length != 4) return false;
    return RegExp(r'^\d{4}$').hasMatch(pin);
  }

  // Generate secure random PIN (for testing)
  String generateRandomPin() {
    final random = DateTime.now().millisecondsSinceEpoch;
    return (random % 10000).toString().padLeft(4, '0');
  }
}
