class Favorite {
  final int? id;
  final String symbol; // BTC, ETH, AAPL, etc.
  final String name; // Bitcoin, Ethereum, Apple Inc.
  final String type; // 'crypto', 'stock'
  final double? currentPrice;
  final double? priceChange24h;
  final double? priceChangePercentage24h;
  final DateTime? lastUpdated;
  final DateTime createdAt;

  Favorite({
    this.id,
    required this.symbol,
    required this.name,
    required this.type,
    this.currentPrice,
    this.priceChange24h,
    this.priceChangePercentage24h,
    this.lastUpdated,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  // Convert Favorite to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'symbol': symbol,
      'name': name,
      'type': type,
      'current_price': currentPrice,
      'price_change_24h': priceChange24h,
      'price_change_percentage_24h': priceChangePercentage24h,
      'last_updated': lastUpdated?.millisecondsSinceEpoch,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  // Create Favorite from Map (database result)
  factory Favorite.fromMap(Map<String, dynamic> map) {
    return Favorite(
      id: map['id']?.toInt(),
      symbol: map['symbol'] ?? '',
      name: map['name'] ?? '',
      type: map['type'] ?? '',
      currentPrice: map['current_price']?.toDouble(),
      priceChange24h: map['price_change_24h']?.toDouble(),
      priceChangePercentage24h: map['price_change_percentage_24h']?.toDouble(),
      lastUpdated: map['last_updated'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['last_updated'])
          : null,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
    );
  }

  // Copy with method for updating favorite
  Favorite copyWith({
    int? id,
    String? symbol,
    String? name,
    String? type,
    double? currentPrice,
    double? priceChange24h,
    double? priceChangePercentage24h,
    DateTime? lastUpdated,
    DateTime? createdAt,
  }) {
    return Favorite(
      id: id ?? this.id,
      symbol: symbol ?? this.symbol,
      name: name ?? this.name,
      type: type ?? this.type,
      currentPrice: currentPrice ?? this.currentPrice,
      priceChange24h: priceChange24h ?? this.priceChange24h,
      priceChangePercentage24h: priceChangePercentage24h ?? this.priceChangePercentage24h,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  // Check if price is going up or down
  bool get isPriceUp => (priceChangePercentage24h ?? 0) > 0;
  bool get isPriceDown => (priceChangePercentage24h ?? 0) < 0;

  // Format price change percentage
  String get formattedPriceChangePercentage {
    if (priceChangePercentage24h == null) return '0.00%';
    final sign = isPriceUp ? '+' : '';
    return '$sign${priceChangePercentage24h!.toStringAsFixed(2)}%';
  }

  // Format current price
  String get formattedCurrentPrice {
    if (currentPrice == null) return '0.00';
    if (type == 'crypto') {
      return '\$${currentPrice!.toStringAsFixed(2)}';
    } else {
      return '\$${currentPrice!.toStringAsFixed(2)}';
    }
  }

  @override
  String toString() {
    return 'Favorite{id: $id, symbol: $symbol, name: $name, type: $type, currentPrice: $currentPrice}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Favorite && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
