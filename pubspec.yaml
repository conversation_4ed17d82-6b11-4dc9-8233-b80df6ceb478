name: personal_finance_app
description: "Kişisel finans yönetimi uygulaması - Lokal SQLite veritabanı ile çalışır"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter

  # UI Icons
  cupertino_icons: ^1.0.8
  
  # Database
  sqflite: ^2.3.0
  path: ^1.8.3
  
  # HTTP requests for API calls
  http: ^1.1.0
  
  # Charts and graphs
  fl_chart: ^0.66.0
  
  # Secure storage and encryption
  shared_preferences: ^2.2.2
  crypto: ^3.0.3
  
  # State management
  provider: ^6.1.1
  
  # Date and time utilities
  intl: ^0.19.0
  
  # Local notifications
  flutter_local_notifications: ^16.3.0
  
  # PIN input widget
  pin_code_fields: ^8.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
