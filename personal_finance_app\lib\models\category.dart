import 'package:flutter/material.dart';

class Category {
  final int? id;
  final String name;
  final String type; // 'income', 'expense'
  final String icon; // Icon name as string
  final String color; // Color as hex string
  final bool isDefault;
  final DateTime createdAt;
  final DateTime updatedAt;

  Category({
    this.id,
    required this.name,
    required this.type,
    this.icon = 'category',
    this.color = '#2196F3',
    this.isDefault = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  // Convert Category to Map for database operations
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'icon': icon,
      'color': color,
      'is_default': isDefault ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  // Create Category from Map (database result)
  factory Category.fromMap(Map<String, dynamic> map) {
    return Category(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      type: map['type'] ?? '',
      icon: map['icon'] ?? 'category',
      color: map['color'] ?? '#2196F3',
      isDefault: (map['is_default'] ?? 0) == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  // Copy with method for updating category
  Category copyWith({
    int? id,
    String? name,
    String? type,
    String? icon,
    String? color,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // Get Color object from hex string
  Color get colorValue {
    return Color(int.parse(color.replaceFirst('#', '0xFF')));
  }

  @override
  String toString() {
    return 'Category{id: $id, name: $name, type: $type, icon: $icon, color: $color}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Category && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Default categories for initial setup
  static List<Category> getDefaultCategories() {
    return [
      // Expense categories
      Category(name: 'Yemek', type: 'expense', icon: 'restaurant', color: '#FF5722', isDefault: true),
      Category(name: 'Ulaşım', type: 'expense', icon: 'directions_car', color: '#2196F3', isDefault: true),
      Category(name: 'Kira', type: 'expense', icon: 'home', color: '#4CAF50', isDefault: true),
      Category(name: 'Alışveriş', type: 'expense', icon: 'shopping_cart', color: '#9C27B0', isDefault: true),
      Category(name: 'Sağlık', type: 'expense', icon: 'local_hospital', color: '#F44336', isDefault: true),
      Category(name: 'Eğlence', type: 'expense', icon: 'movie', color: '#FF9800', isDefault: true),
      Category(name: 'Faturalar', type: 'expense', icon: 'receipt', color: '#607D8B', isDefault: true),
      
      // Income categories
      Category(name: 'Maaş', type: 'income', icon: 'work', color: '#4CAF50', isDefault: true),
      Category(name: 'Freelance', type: 'income', icon: 'computer', color: '#2196F3', isDefault: true),
      Category(name: 'Yatırım', type: 'income', icon: 'trending_up', color: '#FF9800', isDefault: true),
      Category(name: 'Diğer', type: 'income', icon: 'attach_money', color: '#9C27B0', isDefault: true),
    ];
  }
}
