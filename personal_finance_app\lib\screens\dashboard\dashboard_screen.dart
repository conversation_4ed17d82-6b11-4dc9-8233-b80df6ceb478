import 'package:flutter/material.dart';
import '../../widgets/custom_card.dart';
import '../../helpers/app_theme.dart';
import '../../data/database_helper.dart';
import '../../models/account.dart';
import '../../models/transaction.dart' as model;

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  
  double _totalBalance = 0.0;
  List<Account> _accounts = [];
  List<model.Transaction> _recentTransactions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load accounts
      final accounts = await _databaseHelper.getAccounts();
      
      // Calculate total balance
      double total = 0.0;
      for (final account in accounts) {
        total += account.balance;
      }
      
      // Load recent transactions
      final transactions = await _databaseHelper.getTransactions(limit: 5);

      setState(() {
        _accounts = accounts;
        _totalBalance = total;
        _recentTransactions = transactions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Handle error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Veri yüklenirken hata oluştu: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              // TODO: Navigate to notifications
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () {
              // TODO: Navigate to settings
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDashboardData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome Section
                    _buildWelcomeSection(theme),
                    
                    const SizedBox(height: 24),
                    
                    // Total Balance Card
                    _buildTotalBalanceCard(theme),
                    
                    const SizedBox(height: 24),
                    
                    // Quick Actions
                    _buildQuickActions(theme),
                    
                    const SizedBox(height: 24),
                    
                    // Accounts Overview
                    _buildAccountsOverview(theme),
                    
                    const SizedBox(height: 24),
                    
                    // Recent Transactions
                    _buildRecentTransactions(theme),
                    
                    const SizedBox(height: 24),
                    
                    // Market Overview (Placeholder)
                    _buildMarketOverview(theme),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildWelcomeSection(ThemeData theme) {
    final hour = DateTime.now().hour;
    String greeting;
    
    if (hour < 12) {
      greeting = 'Günaydın';
    } else if (hour < 17) {
      greeting = 'İyi öğleden sonra';
    } else {
      greeting = 'İyi akşamlar';
    }

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                greeting,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onBackground.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Finansal durumunuz',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.person_outline,
            color: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildTotalBalanceCard(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [AppTheme.primaryColor, AppTheme.primaryColorDark],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Toplam Bakiye',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${_totalBalance.toStringAsFixed(2)} TRY',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              const Icon(
                Icons.trending_up,
                color: Colors.white70,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '${_accounts.length} hesap',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Hızlı İşlemler',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: QuickActionCard(
                title: 'Gelir Ekle',
                icon: Icons.add_circle_outline,
                color: AppTheme.successColor,
                onTap: () {
                  // TODO: Navigate to add income
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: QuickActionCard(
                title: 'Gider Ekle',
                icon: Icons.remove_circle_outline,
                color: AppTheme.errorColor,
                onTap: () {
                  // TODO: Navigate to add expense
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: QuickActionCard(
                title: 'Transfer',
                icon: Icons.swap_horiz,
                color: AppTheme.warningColor,
                onTap: () {
                  // TODO: Navigate to transfer
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAccountsOverview(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Hesaplarım',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to accounts
              },
              child: const Text('Tümünü Gör'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_accounts.isEmpty)
          CustomCard(
            child: Column(
              children: [
                const Icon(
                  Icons.account_balance_wallet_outlined,
                  size: 48,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  'Henüz hesap eklenmemiş',
                  style: theme.textTheme.bodyLarge,
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Navigate to add account
                  },
                  child: const Text('İlk Hesabını Ekle'),
                ),
              ],
            ),
          )
        else
          ...(_accounts.take(3).map((account) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: BalanceCard(
                  title: account.name,
                  amount: account.balance,
                  currency: account.currency,
                  icon: _getAccountIcon(account.type),
                  onTap: () {
                    // TODO: Navigate to account details
                  },
                ),
              ))),
      ],
    );
  }

  Widget _buildRecentTransactions(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Son İşlemler',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to transactions
              },
              child: const Text('Tümünü Gör'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        if (_recentTransactions.isEmpty)
          CustomCard(
            child: Column(
              children: [
                const Icon(
                  Icons.receipt_long_outlined,
                  size: 48,
                  color: Colors.grey,
                ),
                const SizedBox(height: 16),
                Text(
                  'Henüz işlem yapılmamış',
                  style: theme.textTheme.bodyLarge,
                ),
              ],
            ),
          )
        else
          CustomCard(
            child: Column(
              children: _recentTransactions.map((transaction) {
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: _getTransactionColor(transaction.type).withOpacity(0.1),
                    child: Icon(
                      _getTransactionIcon(transaction.type),
                      color: _getTransactionColor(transaction.type),
                    ),
                  ),
                  title: Text(transaction.category),
                  subtitle: Text(transaction.description ?? ''),
                  trailing: Text(
                    '${transaction.type == 'expense' ? '-' : '+'}${transaction.amount.toStringAsFixed(2)} TRY',
                    style: TextStyle(
                      color: _getTransactionColor(transaction.type),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
      ],
    );
  }

  Widget _buildMarketOverview(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Piyasa Durumu',
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        CustomCard(
          child: Column(
            children: [
              const Icon(
                Icons.trending_up,
                size: 48,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'Piyasa verileri yakında eklenecek',
                style: theme.textTheme.bodyLarge,
              ),
              const SizedBox(height: 8),
              Text(
                'Bitcoin, Ethereum ve borsa endeksleri',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getAccountIcon(String type) {
    switch (type) {
      case 'checking':
        return Icons.account_balance;
      case 'savings':
        return Icons.savings;
      case 'credit':
        return Icons.credit_card;
      case 'investment':
        return Icons.trending_up;
      default:
        return Icons.account_balance_wallet;
    }
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'income':
        return Icons.add_circle;
      case 'expense':
        return Icons.remove_circle;
      case 'transfer':
        return Icons.swap_horiz;
      default:
        return Icons.receipt;
    }
  }

  Color _getTransactionColor(String type) {
    switch (type) {
      case 'income':
        return AppTheme.successColor;
      case 'expense':
        return AppTheme.errorColor;
      case 'transfer':
        return AppTheme.warningColor;
      default:
        return Colors.grey;
    }
  }
}
